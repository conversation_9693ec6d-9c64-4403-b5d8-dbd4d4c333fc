import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { getBlogPostBySlug, getRelatedPosts, blogCategories, blogPosts } from '@/lib/blog-data';
import BlogPostClient from './BlogPostClient';



// Generate metadata for SEO
export async function generateMetadata({ params }: any): Promise<Metadata> {
  const { slug } = params;
  const post = getBlogPostBySlug(slug);
  
  if (!post) {
    return {
      title: 'Artikel Tidak Ditemukan | Gigsta',
      description: 'Artikel yang Anda cari tidak ditemukan.',
    };
  }

  const category = blogCategories.find(cat => cat.slug === post.category);

  return {
    title: `${post.title} | Gigsta Blog`,
    description: post.description,
    keywords: post.tags.join(', '),
    authors: [{ name: post.author }],
    openGraph: {
      title: post.title,
      description: post.description,
      type: 'article',
      url: `https://gigsta.io/blog/${post.slug}`,
      images: [
        {
          url: post.image,
          width: 1200,
          height: 630,
          alt: post.imageAlt,
        },
      ],
      publishedTime: post.publishedAt,
      modifiedTime: post.updatedAt || post.publishedAt,
      section: category?.name,
      tags: post.tags,
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.description,
      images: [post.image],
    },
    alternates: {
      canonical: `https://gigsta.io/blog/${post.slug}`,
    },
  };
}

// Generate static params for all blog posts
export async function generateStaticParams() {
  return blogPosts.map((post) => ({
    slug: post.slug,
  }));
}

// Structured data for SEO
function generateStructuredData(post: any) {
  const category = blogCategories.find(cat => cat.slug === post.category);
  
  if (post.structuredData) {
    return {
      '@context': 'https://schema.org',
      ...post.structuredData,
    };
  }

  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: post.title,
    description: post.seoDescription || post.description,
    image: post.ogImage || post.image,
    author: {
      '@type': 'Organization',
      name: post.author,
      url: 'https://gigsta.io',
    },
    publisher: {
      '@type': 'Organization',
      name: 'Gigsta',
      logo: {
        '@type': 'ImageObject',
        url: 'https://gigsta.io/images/logo.png',
      },
    },
    datePublished: post.publishedAt,
    dateModified: post.updatedAt || post.publishedAt,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `https://gigsta.io/blog/${post.slug}`,
    },
    articleSection: category?.name,
    keywords: (post.keywords && post.keywords.length > 0) ? post.keywords.join(', ') : post.tags.join(', '),
    wordCount: post.estimatedWords || post.content.split(' ').length,
    timeRequired: `PT${post.readingTime}M`,
  };
}

export default async function BlogPostPage({ params }: any) {
  const { slug } = params;
  const post = getBlogPostBySlug(slug);

  if (!post) {
    notFound();
  }

  const relatedPosts = getRelatedPosts(slug, 3);
  const category = blogCategories.find(cat => cat.slug === post.category);
  const structuredData = generateStructuredData(post);

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      {post.faq && post.faq.length > 0 && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'FAQPage',
              mainEntity: post.faq.map((item: { question: string; answer: string }) => ({
                '@type': 'Question',
                name: item.question,
                acceptedAnswer: {
                  '@type': 'Answer',
                  text: item.answer,
                },
              })),
            }),
          }}
        />
      )}

      <BlogPostClient
        post={post}
        relatedPosts={relatedPosts}
        category={category}
      />
    </>
  );
}
